local function debugPrint(...)
    if Config.Debug then
        lib.print.debug('[craftingui]', ...)
    end
end

debugPrint('server.lua loaded')

local QBCore = exports['qb-core']:GetCoreObject()

local PlacedBenches = {}

local function getBenchConfig(model)
    if not Config.craftingBenches then return end
    for _, bench in ipairs(Config.craftingBenches) do
        if bench.model == model then
            return bench
        end
    end
end

local function registerBenchStash(bench)
    local cfg = getBenchConfig(bench.model)
    if not cfg then return end
    local stashId = ('srp_crafting_%s'):format(bench.id)
    exports.ox_inventory:RegisterStash(stashId, cfg.label or 'Crafting Bench', cfg.slots or 20, cfg.weight or 20000, false)
end

-- Create SQL table if it doesn't exist
CreateThread(function()
    exports.oxmysql:execute([[ 
        CREATE TABLE IF NOT EXISTS `srp_crafting_benches` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `owner_cid` VARCHAR(50),
            `bench_model` VARCHAR(100),
            `x` FLOAT,
            `y` FLOAT,
            `z` FLOAT,
            `rot_x` FLOAT,
            `rot_y` FLOAT,
            `rot_z` FLOAT,
            `time_placed` BIGINT,
            INDEX `idx_owner_cid` (`owner_cid`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]])
end)

local function loadBenchesFromSQL()
    exports.oxmysql:execute('SELECT * FROM `srp_crafting_benches`', {}, function(results)
        PlacedBenches = {}
        for _, row in ipairs(results) do
            local bench = {
                id = row.id,
                owner = row.owner_cid,
                model = row.bench_model,
                coords = { x = row.x, y = row.y, z = row.z },
                rot = { x = row.rot_x, y = row.rot_y, z = row.rot_z },
                timePlaced = row.time_placed
            }
            PlacedBenches[row.id] = bench
            registerBenchStash(bench)
        end
        for _, player in pairs(GetPlayers()) do
            local src = tonumber(player)
            for _, bench in pairs(PlacedBenches) do
                TriggerClientEvent('srp-crafting:client:registerBench', src, bench)
            end
        end
    end)
end

AddEventHandler('onResourceStart', function(resource)
    if resource == GetCurrentResourceName() then
        loadBenchesFromSQL()
    end
end)

RegisterNetEvent('srp-crafting:server:placeBench', function(model, coords, rot)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    local ownerCid = Player.PlayerData.citizenid or tostring(src)
    exports.ox_inventory:RemoveItem(src, 'srpcraftingtable', 1)
    exports.oxmysql:insert('INSERT INTO `srp_crafting_benches` (`owner_cid`,`bench_model`,`x`,`y`,`z`,`rot_x`,`rot_y`,`rot_z`,`time_placed`) VALUES (?,?,?,?,?,?,?,?,?)', {
        ownerCid, model, coords.x, coords.y, coords.z, rot.x, rot.y, rot.z, os.time()
    }, function(insertId)
        if not insertId then return end
        local bench = {
            id = insertId,
            owner = ownerCid,
            model = model,
            coords = { x = coords.x, y = coords.y, z = coords.z },
            rot = { x = rot.x, y = rot.y, z = rot.z },
            timePlaced = os.time()
        }
        PlacedBenches[insertId] = bench
        registerBenchStash(bench)
        TriggerClientEvent('srp-crafting:client:registerBench', -1, bench)
    end)
end)

RegisterNetEvent('srp-crafting:server:requestAllBenches', function()
    local src = source
    for _, bench in pairs(PlacedBenches) do
        TriggerClientEvent('srp-crafting:client:registerBench', src, bench)
    end
end)

RegisterNetEvent('craftingui:server:pickupCraftingTable', function(netId, benchId)
    local src = source
    if netId then
        local entity = NetworkGetEntityFromNetworkId(netId)
        if entity and DoesEntityExist(entity) then
            DeleteEntity(entity)
        end
    end
    if benchId and PlacedBenches[benchId] then
        exports.oxmysql:execute('DELETE FROM `srp_crafting_benches` WHERE `id`=?', {benchId})
        PlacedBenches[benchId] = nil
        TriggerClientEvent('srp-crafting:client:removeBench', -1, benchId)
        exports.ox_inventory:ClearInventory(('srp_crafting_%s'):format(benchId))
    end
    exports.ox_inventory:AddItem(src, 'srpcraftingtable', 1)
end)

lib.addCommand('srpcraftingui', {
    help = 'Open crafting UI for a bench',
    params = {
        { name = 'id', type = 'number', help = 'Bench ID', optional = true }
    },
    restricted = 'group.admin'
}, function(source, args)
    TriggerClientEvent('srp-crafting:openBench', source, args.id or 0)
end)

