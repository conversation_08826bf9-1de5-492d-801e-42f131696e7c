Config = {
    Debug = false,
    tabs = {
        { name = "All" },
        { name = "Weapon" },
        { name = "Medical" },
        { name = "Electronics" },
        { name = "farming" },
    },
    crafting = {
        weapon = {
            [1] = {
                name = "lockpick", -- !! for ox weapons it must be upper case (weapon_pistol50 will not work) (WEAPON_PISTOL50 will work)
                type = "Lockpick",
                info = {},             -- item metadata
                rep = 0,
                amount = 1,
                requireBlueprint = false,
                time = 45,
                description = "If you lose your keys a lot this is very useful... Also useful to open your beers",
                costs = {
                    { name = "metalscrap", amount = 8 },
                    { name = "plastic",    amount = 6 },
                    { name = "rubber",     amount = 8 },
                    { name = "copper",     amount = 6 },
                    { name = "iron",       amount = 6 },
                    { name = "glass",      amount = 6 },
                },
                earnedrep = 5, 
            },
            [2] = {
                name = "advancedlockpick", 
                type = "Advanced Lockpick",
                info = {},             -- item metadata
                rep = 0,
                amount = 1,
                requireBlueprint = false,
                time = 45,
                description = "If you lose your keys a lot this is very useful... Also useful to open your beers",
                costs = {
                    { name = "metalscrap", amount = 8 },
                    { name = "plastic",    amount = 6 },
                },
                earnedrep = 1, 
            },
            [3] = {
                name = "accesstool", 
                type = "Access Tool",
                info = {},             -- item metadata
                rep = 0,
                amount = 1,
                requireBlueprint = false,
                time = 45,
                description = "If you lose your keys a lot this is very useful... Also useful to open your beers or cars.",
                costs = {
                    { name = "metalscrap", amount = 8 },
                    { name = "plastic",    amount = 6 },
                },
                earnedrep = 1, 
            },
            [4] = {
                name = "WEAPON_BANDOS", 
                type = "Bandos Weapon",
                info = {},             -- item metadata
                rep = 0,
                amount = 1,
                requireBlueprint = false,
                time = 45,
                description = "If you lose your keys a lot this is very useful... Also useful to open your beers",
                costs = {
                    { name = "metalscrap", amount = 8 },
                    { name = "plastic",    amount = 6 },
                },
                earnedrep = 1, 
            },
            -- Add more weapon recipes here
        },




        medical = {            
            [1] = {
                name = "bandage", 
                type = "Bandage",
                info = {},             -- item metadata
                rep = 0,
                amount = 1,
                requireBlueprint = false,
                time = 45,
                description = "If you lose your keys a lot this is very useful... Also useful to open your beers",
                costs = {
                    { name = "metalscrap", amount = 8 },
                    { name = "plastic",    amount = 6 },
                },
                earnedrep = 1, 
            },
            -- Add more medical recipes here
        },




        electronics = { },




        farming = {
            [1] = {
                name = "basic_fertilizer", 
                type = "Basic Fertilizer",
                info = {},             -- item metadata
                rep = 0,
                amount = 1,
                requireBlueprint = false,
                time = 45,
                description = "If you lose your keys a lot this is very useful... Also useful to open your beers",
                costs = {
                    { name = "metalscrap", amount = 8 },
                    { name = "plastic",    amount = 6 },
                },
                earnedrep = 1, 
            },
            [2] = {
                name = "advanced_fertilizer", 
                type = "Advanced Fertilizer",
                info = {},                -- item metadata
                rep = 0,
                amount = 1,
                requireBlueprint = false,
                time = 45,
                description = "If you lose your keys a lot this is very useful... Also useful to open your beers",
                costs = {
                    { name = "metalscrap", amount = 8 },
                    { name = "plastic",    amount = 6 },
                },
                earnedrep = 1, 
            },
            [3] = {
                name = "premium_fertilizer", 
                type = "Premium Fertilizer",
                info = {},               -- item metadata
                rep = 0,
                amount = 1,
                requireBlueprint = false,
                time = 45,
                description = "If you lose your keys a lot this is very useful... Also useful to open your beers",
                costs = {
                    { name = "metalscrap", amount = 8 },
                    { name = "plastic",    amount = 6 },
                },
                earnedrep = 1, 
            },
            [4] = {
                name = "organic_fertilizer", 
                type = "Organic Fertilizer",
                info = {},               -- item metadata
                rep = 0,
                amount = 1,
                requireBlueprint = false,
                time = 45,
                description = "If you lose your keys a lot this is very useful... Also useful to open your beers",
                costs = {
                    { name = "metalscrap", amount = 8 },
                    { name = "plastic",    amount = 6 },
                },
                earnedrep = 1, 
            },
            [5] = {
                name = "super_fertilizer", 
                type = "Super Fertilizer",
                info = {},             -- item metadata
                rep = 0,
                amount = 1,
                requireBlueprint = false,
                time = 45,
                description = "If you lose your keys a lot this is very useful... Also useful to open your beers",
                costs = {
                    { name = "metalscrap", amount = 8 },
                    { name = "plastic",    amount = 6 },
                },
                earnedrep = 1, 
            },
            [6] = {
                name = "small_farm_plot", 
                type = "Small Farm Plot",
                info = {},                 -- item metadata
                rep = 0,
                amount = 1,
                requireBlueprint = false,
                time = 45,
                description = "If you lose your keys a lot this is very useful... Also useful to open your beers",
                costs = {
                    { name = "metalscrap", amount = 8 },
                    { name = "plastic",    amount = 6 },
                },
                earnedrep = 1, 
            },
            [7] = {
                name = "medium_farm_plot",     
                type = "Medium Farm Plot",
                info = {},                     -- item metadata
                rep = 0,
                amount = 1,
                requireBlueprint = false,
                time = 45,
                description = "If you lose your keys a lot this is very useful... Also useful to open your beers",
                costs = {
                    { name = "metalscrap", amount = 8 },
                    { name = "plastic",    amount = 6 },
                },
                earnedrep = 1,     
            },
            [8] = {
                name = "large_farm_plot",         
                type = "Large Farm Plot",
                info = {},                         -- item metadata
                rep = 0,
                amount = 1,
                requireBlueprint = false,
                time = 45,
                description = "If you lose your keys a lot this is very useful... Also useful to open your beers",
                costs = {
                    { name = "metalscrap", amount = 8 },
                    { name = "plastic",    amount = 6 },
                },
                earnedrep = 1,         
            },
            -- Add more farming recipes here
        },
    },
    PropManagement = {
        gridSize = 15,
        maxSpawnsPerFrame = 10,
        spawnDelay = 60,
        despawnDelay = 60
    },
    craftingBenches = {
        {
            model = "gr_prop_gr_bench_02b",
            label = "Crafting Bench",
            slots = 20,
            weight = 20000
        }
    }
}
