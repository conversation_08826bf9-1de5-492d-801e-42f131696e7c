-- local Config = require 'config'

local function debugPrint(...)
    if Config.Debug then
        lib.print.debug('[craftingui]', ...)
    end
end

-- Debug: wrap SendNUIMessage to print all calls
local _SendNUIMessage = SendNUIMessage
SendNUIMessage = function(data)
    if Config.Debug then
        if json and json.encode then
            lib.print.debug('[craftingui] SendNUIMessage called:', json.encode(data))
        else
            lib.print.debug('[craftingui] SendNUIMessage called:', data and data.type or '(no type)')
        end
    end
    _SendNUIMessage(data)
end

local currentBenchId
local spawnedBenches = {}

local function openCraftingUI(benchId)
    currentBenchId = benchId
    debugPrint('openCraftingUI triggered', benchId)
    local allMaterials = {}
    for _, category in pairs(Config.crafting) do
        for _, recipe in pairs(category) do
            if recipe.costs then
                for _, matObj in ipairs(recipe.costs) do
                    allMaterials[matObj.name] = true
                end
            end
        end
    end

    local materialCounts = {}
    for mat in pairs(allMaterials) do
        materialCounts[mat] = exports.ox_inventory:GetItemCount(mat)
    end

    local QBCore = exports['qb-core']:GetCoreObject()
    local player = QBCore.Functions.GetPlayerData()
    local playerName = player.charinfo and (player.charinfo.firstname .. ' ' .. player.charinfo.lastname) or 'Player Name'
    local playerXP = player.metadata and player.metadata.craftingxp or 0

    SetNuiFocus(true, true)
    SetNuiFocusKeepInput(false)
    SendNUIMessage({
        type = 'ui',
        display = true,
        config = Config,
        materialCounts = materialCounts,
        playerName = playerName,
        playerXP = playerXP,
        benchId = benchId
    })
end

RegisterNUICallback("closeCraftingUI", function(_, cb)
    SetNuiFocus(false, false)
    SetNuiFocusKeepInput(false)
    SendNUIMessage({ type = "ui", display = false })
    cb("ok")
end)

-- Hide UI and release focus on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if (GetCurrentResourceName() == resourceName) then
        SendNUIMessage({ type = "ui", display = false })
        SetNuiFocus(false, false)
        SetNuiFocusKeepInput(false)

        for id, ent in pairs(spawnedBenches) do
            if DoesEntityExist(ent) then
                DeleteEntity(ent)
            end
            spawnedBenches[id] = nil
        end
    end
end)

-- Hide UI and release focus on resource start
AddEventHandler('onResourceStart', function(resourceName)
    if (GetCurrentResourceName() == resourceName) then
        SendNUIMessage({ type = "ui", display = false })
        SetNuiFocus(false, false)
        SetNuiFocusKeepInput(false)
    end
end)

-- Add targeting for crafting bench prop
defineTargeting = function()
    if not Config.craftingBenches then return end
    for _, bench in ipairs(Config.craftingBenches) do
        local propModel = bench.model
        -- Skip the placeable table model to avoid duplicate options
        if propModel == 'gr_prop_gr_bench_02b' then goto continue end
        if exports['ox_target'] then
            exports['ox_target']:addModel(propModel, {
                label = 'Crafting',
                icon = 'fa-solid fa-hammer',
                onSelect = function()
                    TriggerEvent('srp-crafting:openBench', 0)
                end
            })
        else
            -- Fallback: simple proximity + E key
            Citizen.CreateThread(function()
                while true do
                    Citizen.Wait(0)
                    local playerPed = PlayerPedId()
                    local playerCoords = GetEntityCoords(playerPed)
                    local benchObj = GetClosestObjectOfType(playerCoords, 2.0, GetHashKey(propModel), false, false, false)
                    if benchObj ~= 0 then
                        local benchCoords = GetEntityCoords(benchObj)
                        if #(playerCoords - benchCoords) < 2.0 then
                            SetTextComponentFormat('STRING')
                            AddTextComponentString('Press ~INPUT_CONTEXT~ to use Crafting')
                            DisplayHelpTextFromStringLabel(0, 0, 1, -1)
                            if IsControlJustReleased(0, 38) then -- E
                                TriggerEvent('srp-crafting:openBench', 0)
                                Citizen.Wait(1000)
                            end
                        end
                    end
                end
            end)
        end
        ::continue::
    end
end

Citizen.CreateThread(function()
    defineTargeting()
end)

debugPrint('client.lua loaded')

-- Grid based bench management
Config.PropManagement = Config.PropManagement or {}
local grid = {}
local loadedcells = {}
local benches = {}
local lastcell = vector3(0,0,0)

local function spawnBench(data)
    if spawnedBenches[data.id] and DoesEntityExist(spawnedBenches[data.id]) then return end
    local model = GetHashKey(data.model)
    RequestModel(model)
    while not HasModelLoaded(model) do Wait(0) end
    local entity = CreateObject(model, data.coords.x, data.coords.y, data.coords.z, true, true, false)
    SetEntityAsMissionEntity(entity, true, true)
    SetEntityCollision(entity, true, true)
    FreezeEntityPosition(entity, true)
    SetEntityRotation(entity, data.rot.x, data.rot.y, data.rot.z, 0, false)
    PlaceObjectOnGroundProperly(entity)
    spawnedBenches[data.id] = entity
    if exports['ox_target'] then
        exports['ox_target']:addLocalEntity(entity, {
            {
                label = 'Crafting',
                icon = 'fa-solid fa-hammer',
                onSelect = function()
                    TriggerEvent('srp-crafting:openBench', data.id)
                end
            },
            {
                label = 'Open Storage',
                icon = 'fa-solid fa-box-open',
                onSelect = function()
                    local stashId = 'srp_crafting_' .. data.id
                    exports.ox_inventory:openInventory('stash', stashId)
                end
            },
            {
                label = 'Pick Up Table',
                icon = 'fa-solid fa-box',
                onSelect = function(ctx)
                    local ent = type(ctx) == 'table' and ctx.entity or ctx
                    local netId = NetworkGetNetworkIdFromEntity(ent)
                    TriggerServerEvent('craftingui:server:pickupCraftingTable', netId, data.id)
                end
            }
        })
    end
end

local function deleteBench(data)
    local entity = spawnedBenches[data.id]
    if entity and DoesEntityExist(entity) then
        DeleteEntity(entity)
    end
    spawnedBenches[data.id] = nil
end

local function enterCell(cell)
    if not grid[cell.x] or not grid[cell.x][cell.y] or not grid[cell.x][cell.y][cell.z] then return end
    if not loadedcells[cell.x] then loadedcells[cell.x] = {} end
    if not loadedcells[cell.x][cell.y] then loadedcells[cell.x][cell.y] = {} end
    if loadedcells[cell.x][cell.y][cell.z] then return end
    loadedcells[cell.x][cell.y][cell.z] = true
    for _, bench in pairs(grid[cell.x][cell.y][cell.z]) do
        spawnBench(bench)
        Wait(Config.PropManagement.spawnDelay or 60)
    end
end

local function exitCell(cell)
    if not loadedcells[cell.x] or not loadedcells[cell.x][cell.y] or not loadedcells[cell.x][cell.y][cell.z] then return end
    loadedcells[cell.x][cell.y][cell.z] = nil
    if grid[cell.x] and grid[cell.x][cell.y] and grid[cell.x][cell.y][cell.z] then
        for _, bench in pairs(grid[cell.x][cell.y][cell.z]) do
            deleteBench(bench)
            Wait(Config.PropManagement.despawnDelay or 60)
        end
    end
end

local function getCell(coords)
    return vector3(
        math.floor(coords.x / Config.PropManagement.gridSize),
        math.floor(coords.y / Config.PropManagement.gridSize),
        math.floor(coords.z / Config.PropManagement.gridSize)
    )
end

CreateThread(function()
    while not cache.ped do Wait(500) end
    local coords = GetEntityCoords(cache.ped)
    local cell = getCell(coords)
    lastcell = cell
    while true do
        Wait(500)
        coords = GetEntityCoords(cache.ped)
        cell = getCell(coords)
        if lastcell == cell and next(loadedcells) then goto continue end
        for xo, v in pairs(loadedcells) do
            for yo, v2 in pairs(v) do
                for zo, _ in pairs(v2) do
                    if math.abs(cell.x - xo) > 1 or math.abs(cell.y - yo) > 1 or math.abs(cell.z - zo) > 1 then
                        exitCell(vector3(xo, yo, zo))
                    end
                end
            end
        end
        for xi = cell.x-1, cell.x+1 do
            for yi = cell.y-1, cell.y+1 do
                for zi = cell.z-1, cell.z+1 do
                    if not loadedcells[xi] then loadedcells[xi] = {} end
                    if not loadedcells[xi][yi] then loadedcells[xi][yi] = {} end
                    if not loadedcells[xi][yi][zi] then
                        enterCell(vector3(xi, yi, zi))
                    end
                end
            end
        end
        lastcell = cell
        ::continue::
    end
end)

local function registerBench(bench)
    benches[bench.id] = bench
    local cell = getCell(bench.coords)
    bench.cell = cell
    if not grid[cell.x] then grid[cell.x] = {} end
    if not grid[cell.x][cell.y] then grid[cell.x][cell.y] = {} end
    if not grid[cell.x][cell.y][cell.z] then grid[cell.x][cell.y][cell.z] = {} end
    grid[cell.x][cell.y][cell.z][bench.id] = bench
    if loadedcells[cell.x] and loadedcells[cell.x][cell.y] and loadedcells[cell.x][cell.y][cell.z] then
        spawnBench(bench)
    end
end

RegisterNetEvent('srp-crafting:client:registerBench', function(bench)
    registerBench(bench)
end)

RegisterNetEvent('srp-crafting:client:removeBench', function(benchId)
    local bench = benches[benchId]
    if not bench then return end
    deleteBench(bench)
    local cell = bench.cell
    if grid[cell.x] and grid[cell.x][cell.y] and grid[cell.x][cell.y][cell.z] then
        grid[cell.x][cell.y][cell.z][benchId] = nil
    end
    benches[benchId] = nil
end)

function SetupCraftingBenches()
    TriggerServerEvent('srp-crafting:server:requestAllBenches')
end

AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        Wait(2000)
        SetupCraftingBenches()
    end
end)

RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    SetupCraftingBenches()
end)

RegisterNetEvent('srp-crafting:openBench', function(id)
    debugPrint('srp-crafting:openBench event', id)
    openCraftingUI(id)
end)

local isPlacing = false
local currentPlacementEntity = nil

local function startPlacement()
    if isPlacing then
        debugPrint('Already placing!')
        return
    end
    local model = Config.craftingBenches and Config.craftingBenches[1] and Config.craftingBenches[1].model or 'gr_prop_gr_bench_02b'
    isPlacing = true
    debugPrint('Loading model:', model)
    RequestModel(model)
    while not HasModelLoaded(model) do Wait(0) end
    local playerPed = PlayerPedId()
    local offset = GetOffsetFromEntityInWorldCoords(playerPed, 0, 2.0, 0)
    currentPlacementEntity = CreateObject(GetHashKey(model), offset.x, offset.y, offset.z, true, false, false)
    SetEntityAlpha(currentPlacementEntity, 200, false)
    SetEntityCollision(currentPlacementEntity, false, false)
    debugPrint('Object created, starting gizmo placement')
    local objectPositionData = exports.object_gizmo:useGizmo(currentPlacementEntity)
    if objectPositionData then
        debugPrint('Placement confirmed!')
        local coords = objectPositionData.position
        local rot = objectPositionData.rotation
        TriggerServerEvent('srp-crafting:server:placeBench', model, coords, rot)
    else
        debugPrint('Placement cancelled!')
    end
    if DoesEntityExist(currentPlacementEntity) then
        DeleteEntity(currentPlacementEntity)
    end
    SetModelAsNoLongerNeeded(model)
    isPlacing = false
    currentPlacementEntity = nil
end

RegisterNetEvent('srpcrafting:usecraftingtable', function(item, metadata)
    debugPrint('srpcrafting:usecraftingtable event', item)
    startPlacement()
end)
